<template>
  <div>
    <!-- 主详情对话框 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :title="detailTitle"
      :before-close="handleClose"
      fullscreen
    >
      <!-- 接报信息详情对话框 - Component -->
      <div v-loading="loading" class="detail-content">
        <!-- 主体内容区域 - 左右布局 -->
        <el-row :gutter="20" class="main-layout">
          <!-- 左侧：接报信息详情区域 -->
          <el-col :span="12" class="left-content">
            <!-- 顶部核心信息区域 -->
            <div class="core-info-section">
              <div class="section-title">
                <i class="el-icon-info"></i>
                <span>核心信息</span>
              </div>
              <div class="core-info-grid">
                <!-- 事件标题 - 优先级最高，占据更大空间 -->
                <div
                  class="core-info-item priority-high"
                  v-if="getEventTitle()"
                >
                  <div class="info-icon">
                    <i class="el-icon-document"></i>
                  </div>
                  <div class="info-content">
                    <div class="info-label">事件标题</div>
                    <div class="info-value title-text">
                      {{ getEventTitle() }}
                    </div>
                  </div>
                </div>

                <!-- 事件类型 -->
                <div class="core-info-item" v-if="detailData.infoType">
                  <div class="info-icon type-icon">
                    <i class="el-icon-collection-tag"></i>
                  </div>
                  <div class="info-content">
                    <div class="info-label">事件类型</div>
                    <div class="info-value type-text">
                      {{ detailData.infoType }}
                    </div>
                  </div>
                </div>

                <!-- 伤亡情况 - 重要信息，特殊样式 -->
                <div
                  class="core-info-item casualty-item"
                  v-if="getCasualtyInfo() && getCasualtyInfo() !== '0死0伤'"
                >
                  <div class="info-icon casualty-icon">
                    <i class="el-icon-warning"></i>
                  </div>
                  <div class="info-content">
                    <div class="info-label">伤亡情况</div>
                    <div class="info-value casualty-info">
                      {{ getCasualtyInfo() }}
                    </div>
                  </div>
                </div>

                <!-- 事发时间 -->
                <div class="core-info-item" v-if="detailData.infoTime">
                  <div class="info-icon time-icon">
                    <i class="el-icon-time"></i>
                  </div>
                  <div class="info-content">
                    <div class="info-label">事发时间</div>
                    <div class="info-value time-text">
                      {{ formatDate(detailData.infoTime) }}
                    </div>
                  </div>
                </div>

                <!-- 事发地点 -->
                <div
                  class="core-info-item location-item"
                  v-if="detailData.infoLocationDetail"
                >
                  <div class="info-icon location-icon">
                    <i class="el-icon-location"></i>
                  </div>
                  <div class="info-content">
                    <div class="info-label">事发地点</div>
                    <div class="info-value location-text">
                      {{ detailData.infoLocationDetail }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 过程信息列表 -->
            <div class="process-section">
              <div class="section-header">
                <div class="section-title-area">
                  <h3
                    class="section-title"
                    @click="toggleSort"
                    :class="{ sortable: true }"
                  >
                    <i class="el-icon-time"></i>
                    过程信息
                    <div class="section-title">
                      <i class="el-icon-info"></i>
                      <span>核心信息</span>
                    </div>
                    <i class="sort-icon" :class="getSortIconClass()"></i>
                  </h3>
                  <div class="sort-indicator" v-if="sortOrder">
                    {{ getSortDescription() }}
                  </div>
                </div>
                <div class="header-controls">
                  <div class="filter-controls">
                    <el-select
                      v-model="selectedProcessType"
                      placeholder="全部"
                      size="small"
                      style="width: 120px"
                      @change="handleProcessTypeChange"
                    >
                      <el-option label="全部" value=""></el-option>
                      <el-option
                        v-for="option in processTypeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      ></el-option>
                    </el-select>
                    <el-button
                      type="text"
                      icon="el-icon-refresh"
                      @click="refreshProcessData"
                      style="margin-left: 10px"
                    >
                      刷新
                    </el-button>
                    <div class="control-actions">
                      <el-checkbox
                        v-model="selectAll"
                        @change="handleSelectAll"
                      >
                        全选
                      </el-checkbox>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 过程信息时间轴 -->
              <div class="process-timeline-container">
                <div
                  v-if="filteredProcessData && filteredProcessData.length > 0"
                  class="process-timeline"
                  :class="{
                    'has-multiple-items': filteredProcessData.length > 1,
                  }"
                  :style="timelineStyle"
                >
                  <div
                    v-for="(item, index) in filteredProcessData"
                    :key="item.id || index"
                    class="process-item-new"
                  >
                    <!-- 左侧：时间 -->
                    <div class="process-time-section">
                      <div class="process-time">
                        {{ formatDateTime(item.courseTime || item.createTime) }}
                      </div>
                    </div>

                    <!-- 中间：操作类型标签 -->
                    <div class="process-type-section">
                      <div class="operation-type-tag">
                        {{ getOperatorDisplayName(item) }}
                      </div>
                    </div>

                    <!-- 右侧：内容和复选框 -->
                    <div class="process-content-section">
                      <div
                        class="process-content-text"
                        @click="showProcessDetail(item)"
                      >
                        {{ getProcessContent(item) }}
                      </div>
                      <el-checkbox
                        :value="item.selected || false"
                        @change="handleItemSelect(item, $event)"
                        @click.stop
                        class="process-checkbox"
                      ></el-checkbox>
                    </div>
                  </div>
                </div>

                <!-- 无数据状态 -->
                <div v-else class="no-data">
                  <i class="el-icon-document"></i>
                  <p>{{ loading ? "加载过程信息中..." : "暂无过程信息" }}</p>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 右侧：预留功能区域 -->
          <el-col :span="12" class="right-content">
            <div class="reserved-section">
              <div class="reserved-content">
                <i class="el-icon-setting"></i>
                <h3>功能开发中</h3>
                <p>此区域预留给后续功能开发</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 过程详情弹框 -->
    <el-dialog
      :visible.sync="processDetailDialogVisible"
      title="过程详情"
      width="70%"
      :before-close="closeProcessDetailDialog"
      :append-to-body="true"
      :modal-append-to-body="false"
      class="process-detail-dialog"
    >
      <div v-loading="detailLoading" class="process-detail-content">
        <div
          v-if="currentProcessItem && !detailLoading"
          class="detail-content-area"
        >
          <!-- 责任部门 -->
          <div class="detail-item">
            <div class="detail-label">责任部门：</div>
            <div class="detail-value">
              {{
                currentProcessItem.responsibleDepartment ||
                currentProcessItem.eventReportingUnit ||
                currentProcessItem.originalData?.courseName ||
                "-"
              }}
            </div>
          </div>

          <!-- 事件标题 -->
          <div class="detail-item">
            <div class="detail-label">事件标题：</div>
            <div class="detail-value">
              {{
                currentProcessItem.eventTitle ||
                currentProcessItem.originalData?.infoTitle ||
                "-"
              }}
            </div>
          </div>

          <!-- 事发时间 -->
          <div class="detail-item">
            <div class="detail-label">事发时间：</div>
            <div class="detail-value">
              {{ formatDate(currentProcessItem.originalData?.infoTime) || "-" }}
            </div>
          </div>

          <!-- 事发地点 -->
          <div class="detail-item">
            <div class="detail-label">事发地点：</div>
            <div class="detail-value">
              {{ currentProcessItem.originalData?.infoLocationDetail || "-" }}
            </div>
          </div>

          <!-- 事件详情 -->
          <div class="detail-item full-content">
            <div class="detail-label">事件详情：</div>
            <div class="detail-value detail-textarea">
              {{
                currentProcessItem.eventInfo ||
                currentProcessItem.originalData?.courseInfo ||
                "暂无详细信息"
              }}
            </div>
          </div>

          <!-- 上报信息 -->
          <div class="detail-item">
            <div class="detail-label">上报时间：</div>
            <div class="detail-value">
              {{ formatDate(currentProcessItem.createTime) || "-" }}
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-label">上报单位：</div>
            <div class="detail-value">
              {{
                currentProcessItem.eventReportingUnit ||
                currentProcessItem.originalData?.courseName ||
                "-"
              }}
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-label">上报人：</div>
            <div class="detail-value">
              {{ currentProcessItem.eventReportingUser || "-" }}
            </div>
          </div>

          <!-- 伤亡统计 -->
          <div class="casualty-summary">
            <div class="casualty-item">
              <span class="casualty-label">死亡：</span>
              <span class="casualty-number death">{{
                currentProcessItem.deathNum || 0
              }}</span>
            </div>
            <div class="casualty-item">
              <span class="casualty-label">失联：</span>
              <span class="casualty-number missing">{{
                currentProcessItem.missingNum || 0
              }}</span>
            </div>
            <div class="casualty-item">
              <span class="casualty-label">重伤：</span>
              <span class="casualty-number severe">{{
                currentProcessItem.severeInjuryNum || 0
              }}</span>
            </div>
            <div class="casualty-item">
              <span class="casualty-label">轻伤：</span>
              <span class="casualty-number light">{{
                currentProcessItem.lightInjuryNum || 0
              }}</span>
            </div>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div v-else-if="!detailLoading" class="no-selection">
          <i class="el-icon-info"></i>
          <p>暂无详情信息</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { receiveInformationApi, systemManagementApi } from "@/api";
import { mapState } from "vuex";

export default {
  name: "ReceiveInformationDetail",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    detailId: {
      type: [String, Number],
      default: "",
    },
    detailTitle: {
      type: String,
      default: "接报信息详情",
    },
  },
  data() {
    return {
      loading: false,
      detailLoading: false, // 过程详情加载状态
      dataLoaded: false, // 跟踪数据是否已加载
      processDetailDialogVisible: false, // 控制过程详情弹框显示
      detailData: {}, // 主详情数据
      processData: [], // 过程信息列表
      filteredProcessData: [], // 过滤后的过程信息列表
      currentProcessItem: null, // 当前选中的过程信息
      processTypeOptions: [], // 过程类型选项
      selectedProcessType: "", // 选中的过程类型
      selectAll: false, // 全选状态
      sortOrder: "desc", // 排序顺序：asc升序，desc降序
    };
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (val && this.detailId && !this.dataLoaded && !this.loading) {
          // 对话框打开且有detailId时加载数据
          this.loadDetailData();
        } else if (!val) {
          // 关闭对话框时重置状态
          this.resetDialogState();
        }
      },
      immediate: false,
    },
    detailId: {
      handler(val, oldVal) {
        // 只有当detailId变化且对话框可见、新ID有效、不同于旧ID时才加载数据
        if (this.dialogVisible && val && val !== oldVal && !this.loading) {
          this.resetDialogState();
          this.loadDetailData();
        }
      },
      immediate: false,
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),

    // 获取当前用户名
    currentUserName() {
      return this.userInfo?.name || "";
    },

    // 计算时间轴连接线样式
    timelineStyle() {
      const itemCount = this.filteredProcessData?.length || 0;

      // 只有1个模块时不显示连接线
      if (itemCount <= 1) {
        return {
          "--timeline-height": "0px",
        };
      }

      // 计算连接线高度
      // 每个模块高度约为：padding(32px) + content(约60px) + margin(20px) = 112px
      // 连接线从第一个模块中心到最后一个模块中心
      const itemHeight = 112; // 单个模块的大致高度
      const connectionHeight = (itemCount - 1) * itemHeight;

      return {
        "--timeline-height": `${connectionHeight}px`,
      };
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },

    // 获取事件标题
    getEventTitle() {
      return (
        this.detailData.infoTitle ||
        this.detailData.eventTitle ||
        this.detailData.title ||
        ""
      );
    },

    // 获取伤亡情况信息
    getCasualtyInfo() {
      const death = this.detailData.deathNum || 0;
      const missing = this.detailData.missingNum || 0;
      const severe = this.detailData.severeInjuryNum || 0;
      const light = this.detailData.lightInjuryNum || 0;

      const total = death + missing + severe + light;
      if (total === 0) {
        return "0死0伤";
      }

      return `${death}死${missing}失联${severe}重伤${light}轻伤`;
    },

    // 处理全选操作
    handleSelectAll(value) {
      // 暂时只是UI展示，后续实现具体逻辑
      console.log("全选状态:", value);
      // 更新所有项目的选中状态
      this.filteredProcessData.forEach((item) => {
        this.$set(item, "selected", value);
      });
    },

    // 获取操作人姓名
    getOperatorName(item) {
      return (
        item.eventReportingUser ||
        item.originalData?.operator ||
        item.originalData?.createUser ||
        "值班员"
      );
    },

    // 获取操作类型
    getOperationType(item) {
      return (
        item.processTypeName ||
        item.originalData?.courseTypeName ||
        this.getOperationTypeByContent(item)
      );
    },

    // 根据内容推断操作类型
    getOperationTypeByContent(item) {
      const content = item.eventInfo || item.originalData?.courseInfo || "";

      if (content.includes("启动响应")) return "启动响应";
      if (content.includes("发送京办")) return "发送京办";
      if (content.includes("信息同步")) return "信息同步";
      if (content.includes("发送短信")) return "发送短信";
      if (content.includes("信息核实")) return "信息核实";
      if (content.includes("现场处置")) return "现场处置";
      if (content.includes("情况汇报")) return "情况汇报";

      return "系统接报";
    },

    // 获取操作人显示名称（用于新布局）
    getOperatorDisplayName(item) {
      // 根据接口数据，优先使用 courseName，如果是"公安局"等部门名称，显示为"值班员"
      const courseName = item.courseName || item.originalData?.courseName;
      const courseTypeName =
        item.courseTypeName || item.originalData?.courseTypeName;

      // 如果是系统接报类型，显示为"值班员"
      if (courseTypeName === "系统接报" || courseTypeName?.includes("接报")) {
        return "值班员";
      }

      // 如果是部门名称，转换为值班员
      if (
        courseName &&
        (courseName.includes("局") ||
          courseName.includes("中心") ||
          courseName.includes("部门"))
      ) {
        return "值班员";
      }

      return courseName || "值班员";
    },

    // 获取过程内容（用于新布局）
    getProcessContent(item) {
      const courseInfo = item.courseInfo || item.originalData?.courseInfo;
      const eventInfo = item.eventInfo;

      // 如果内容包含"首报"等关键词，转换为更友好的显示
      if (courseInfo) {
        if (courseInfo.includes("首报")) {
          return "向密云区应急指挥中心、市公安局公安交通管理局进行事件信息同步。";
        }
        if (courseInfo.includes("信息同步")) {
          return "向相关部门进行事件信息同步。";
        }
        return courseInfo;
      }

      return eventInfo || "暂无描述";
    },

    // 处理单项选择
    handleItemSelect(item, value) {
      this.$set(item, "selected", value);
      // 检查是否所有项目都被选中
      const allSelected = this.filteredProcessData.every(
        (item) => item.selected
      );
      this.selectAll = allSelected;
    },

    // 切换排序
    toggleSort() {
      try {
        // 如果当前是降序或无排序，切换为升序
        if (this.sortOrder === "desc" || !this.sortOrder) {
          this.sortOrder = "asc";
        } else {
          // 如果当前是升序，切换为降序
          this.sortOrder = "desc";
        }

        // 执行排序
        this.sortProcessData();
      } catch (error) {
        console.error("排序失败:", error);
        this.$message.error("排序失败，请重试");
      }
    },

    // 获取排序图标样式
    getSortIconClass() {
      if (!this.sortOrder) {
        return "el-icon-sort";
      }
      return this.sortOrder === "asc" ? "el-icon-sort-up" : "el-icon-sort-down";
    },

    // 获取排序描述
    getSortDescription() {
      if (!this.sortOrder) return "";
      return this.sortOrder === "asc" ? "升序" : "降序";
    },

    // 排序过程数据
    sortProcessData() {
      try {
        if (
          !this.filteredProcessData ||
          this.filteredProcessData.length === 0 ||
          !this.sortOrder
        ) {
          return;
        }

        console.log(
          `开始排序: 顺序=${this.sortOrder}, 数据量=${this.filteredProcessData.length}`
        );

        this.filteredProcessData.sort((a, b) => {
          // 只按时间排序
          const valueA = new Date(a.createTime || 0).getTime();
          const valueB = new Date(b.createTime || 0).getTime();

          let result = 0;
          if (valueA < valueB) {
            result = -1;
          } else if (valueA > valueB) {
            result = 1;
          }

          // 根据排序顺序调整结果
          return this.sortOrder === "desc" ? -result : result;
        });

        // 强制更新视图
        this.$forceUpdate();
      } catch (error) {
        console.error("排序过程数据失败:", error);
      }
    },

    // 重置对话框状态
    resetDialogState() {
      this.dataLoaded = false;
      this.processDetailDialogVisible = false;
      this.currentProcessItem = null;
      this.detailData = {};
      this.processData = [];
      this.filteredProcessData = [];
      this.processTypeOptions = [];
      this.selectedProcessType = "";
      this.loading = false;
      this.detailLoading = false;
      // 重置排序状态
      this.sortOrder = "desc";
      this.selectAll = false;
    },

    // 统一加载详情数据的方法
    async loadDetailData() {
      // 防止重复调用的多重检查
      if (!this.detailId || this.loading || this.dataLoaded) {
        return;
      }

      // 设置加载状态，防止重复调用
      this.loading = true;
      this.dataLoaded = false;

      // 重置详情弹框
      this.processDetailDialogVisible = false;
      this.currentProcessItem = null;

      try {
        // 并行加载，提升性能
        const [detailResult, processResult, typeOptionsResult] =
          await Promise.allSettled([
            this.fetchDetailData(),
            this.fetchProcessList(),
            this.loadProcessTypeOptions(),
          ]);

        // 检查各个请求的结果
        let hasError = false;
        const errors = [];

        if (detailResult.status === "rejected") {
          hasError = true;
          errors.push(
            `详情数据加载失败: ${
              detailResult.reason?.message || detailResult.reason
            }`
          );
          console.error("详情数据加载失败:", detailResult.reason);
        }

        if (processResult.status === "rejected") {
          hasError = true;
          errors.push(
            `过程信息加载失败: ${
              processResult.reason?.message || processResult.reason
            }`
          );
          console.error("过程信息加载失败:", processResult.reason);
        }

        if (typeOptionsResult.status === "rejected") {
          hasError = true;
          errors.push(
            `过程类型选项加载失败: ${
              typeOptionsResult.reason?.message || typeOptionsResult.reason
            }`
          );
          console.error("过程类型选项加载失败:", typeOptionsResult.reason);
          // 设置默认的过程类型选项
          this.processTypeOptions = [];
        }

        // 如果有错误，显示错误信息，但不阻止页面显示
        if (hasError) {
          this.$message.warning(
            `部分数据加载失败，但可以继续使用: ${errors.join("; ")}`
          );
        }

        this.dataLoaded = true;
      } catch (error) {
        console.error("加载详情数据失败:", error);
        this.$message.error("加载详情数据失败: " + (error.message || error));
        this.dataLoaded = true; // 即使失败也标记为已加载，避免重复请求
      } finally {
        this.loading = false;
      }
    },

    // 获取收件人信息
    getRecipientInfo() {
      // 根据实际数据结构返回收件人信息
      const recipients = [];
      if (this.detailData.infoReportingUnit) {
        recipients.push(this.detailData.infoReportingUnit);
      }
      if (this.currentUserName) {
        recipients.push(`${this.currentUserName}(77)`);
      }
      return recipients.length > 0
        ? recipients.join(", ")
        : "系统接报(77), 市政府值班室(36/36), 市委市政府应急值班(10/10), 高新区应急值班(10/10), 高新区应急值班室(19/29)";
    },

    // 刷新过程数据
    async refreshProcessData() {
      if (!this.detailId) return;

      this.loading = true;
      try {
        await this.fetchProcessList();
        this.$message.success("过程信息刷新成功");
      } catch (error) {
        console.error("刷新过程信息失败:", error);
        this.$message.error("刷新过程信息失败");
      } finally {
        this.loading = false;
      }
    },

    // 发送操作
    handleSend() {
      this.$message.success("发送成功");
      this.handleClose();
    },

    // 显示过程信息详情
    async showProcessDetail(item) {
      if (!item || !item.id) return;

      // 立即显示详情弹框并设置loading状态
      this.processDetailDialogVisible = true;
      this.detailLoading = true;
      this.currentProcessItem = null;

      try {
        // 简化请求参数，只传递必要的 otherId 和 otherType
        const requestParams = {
          otherId: item.originalData?.otherId || this.detailId,
          otherType: item.originalData?.otherType || "1",
        };

        // 调试日志：输出关键参数
        console.log("查询事件过程详情参数:", {
          otherId: requestParams.otherId,
          otherType: requestParams.otherType,
        });

        // 调用详情接口获取完整的过程信息
        const response = await receiveInformationApi.queryEventProcessInfo(
          requestParams
        );

        if (response && response.code === 0) {
          // 使用接口返回的详细数据，根据实际接口响应结构进行映射
          this.currentProcessItem = {
            ...item,
            // 基本信息
            id: response.data?.id || item.id,
            eventId: response.data?.reportInfoId || item.eventId,
            eventTitle: response.data?.infoTitle || item.eventTitle,
            eventInfo: response.data?.eventInfo || item.eventInfo,
            createTime: response.data?.createTime || item.createTime,
            updateTime: response.data?.updateTime || item.updateTime,

            // 地理位置信息
            infoDistrict: response.data?.infoDistrict,
            infoTownshipStreet: response.data?.infoTownshipStreet,
            infoLocationDetail: response.data?.infoLocationDetail,
            infoLongitude: response.data?.infoLongitude,
            infoLatitude: response.data?.infoLatitude,

            // 事件分类信息
            infoType: response.data?.infoType,
            infoChildType: response.data?.infoChildType,
            infoThirdType: response.data?.infoThirdType,

            // 伤亡统计
            deathNum: response.data?.deathNum || 0,
            missingNum: response.data?.missingNum || 0,
            severeInjuryNum: response.data?.severeInjuryNum || 0,
            lightInjuryNum: response.data?.lightInjuryNum || 0,

            // 上报信息
            infoReportingMethod: response.data?.infoReportingMethod,
            infoReportingUnit:
              response.data?.infoReportingUnit ||
              item.eventReportingUnit ||
              "-",
            infoReportingUser: response.data?.infoReportingUser,
            eventReportingUnit:
              response.data?.infoReportingUnit ||
              item.eventReportingUnit ||
              "-",

            // 责任部门（使用上报单位作为责任部门）
            responsibleDepartment:
              response.data?.infoReportingUnit ||
              item.eventReportingUnit ||
              "-",

            // 保留原始数据
            originalData: response.data,
          };
        } else {
          // 如果接口调用失败，使用列表数据作为备用
          console.warn("获取过程详情失败，使用列表数据:", response?.message);
          this.currentProcessItem = {
            ...item,
            eventReportingUnit:
              item.eventReportingUnit ||
              this.detailData.infoReportingUnit ||
              "-",
          };
        }
      } catch (error) {
        console.error("获取过程详情出错:", error);
        // 出错时使用列表数据作为备用
        this.currentProcessItem = {
          ...item,
          eventReportingUnit:
            item.eventReportingUnit || this.detailData.infoReportingUnit || "-",
        };
      } finally {
        // 无论成功还是失败，都要关闭loading状态
        this.detailLoading = false;
      }
    },

    // 关闭过程详情弹框
    closeProcessDetailDialog() {
      this.processDetailDialogVisible = false;
      this.currentProcessItem = null;
    },

    // 获取主要详情数据
    async fetchDetailData() {
      if (!this.detailId) return;

      try {
        const response = await receiveInformationApi.queryReportInfo({
          id: this.detailId,
        });

        if (response && response.code === 0) {
          this.detailData = response.data || {};
        } else {
          this.$message.error(response?.message || "获取详情失败");
          throw new Error(response?.message || "获取详情失败");
        }
      } catch (error) {
        console.error("获取详情出错:", error);
        this.$message.error("获取详情失败");
        throw error;
      }
    },

    // 获取过程信息列表 - 使用新的API接口
    async fetchProcessList() {
      if (!this.detailId) return;

      try {
        // 构建请求参数，根据接口文档使用正确的参数名
        const params = {
          id: this.detailId, // 事件ID
        };

        // 如果有选中的过程类型，添加过滤条件
        if (this.selectedProcessType) {
          params.processType = this.selectedProcessType;
        }

        const response = await receiveInformationApi.queryEventProcessInfoList(
          params
        );

        if (response && response.code === 0) {
          // 接口返回的data直接是数组
          let processData = [];
          if (Array.isArray(response.data)) {
            // 调试日志：输出原始列表数据
            console.log("过程信息列表原始数据:", response.data);

            // 根据实际接口响应数据结构进行字段映射
            processData = response.data.map((item) => {
              const mappedItem = {
                id: item.id,
                eventId: this.detailId, // 使用当前事件ID，而不是不存在的 otherId
                reportId: item.reportId, // 报告ID
                createTime: item.courseTime || item.createTime, // 过程时间
                processType: item.courseType, // 过程类型
                processTypeName: item.courseTypeName, // 过程类型名称
                eventInfo: item.courseInfo, // 过程信息内容
                eventTitle: item.courseName, // 过程标题
                eventReportingUnit: item.courseName || "-", // 上报单位（使用courseName）
                eventReportingUser: "-", // 上报用户（接口未返回）
                responsibleDepartment: item.courseName || "-", // 责任部门
                updateTime: item.updateTime,
                // 保留原始数据，完全基于接口返回的字段
                originalData: item,
              };

              // 调试日志：输出接口实际返回的数据结构
              console.log(`过程项 ${item.id} 接口原始数据:`, item);
              console.log(`过程项 ${item.id} 可用字段:`, Object.keys(item));
              console.log(`过程项 ${item.id} 关键字段检查:`, {
                id: item.id,
                courseInfo: item.courseInfo,
                courseName: item.courseName,
                courseTime: item.courseTime,
                courseType: item.courseType,
                courseTypeName: item.courseTypeName,
                createTime: item.createTime,
                reportId: item.reportId,
                updateTime: item.updateTime,
                otherId: item.otherId,
                otherType: item.otherType,
              });

              return mappedItem;
            });
          } else if (response.data && Array.isArray(response.data.items)) {
            processData = response.data.items;
          } else if (response.data && Array.isArray(response.data.records)) {
            processData = response.data.records;
          }

          this.processData = processData || [];

          // 初始化过滤数据
          this.filterProcessData();

          // 强制更新视图
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        } else {
          this.$message.error(response?.message || "获取过程信息失败");
          throw new Error(response?.message || "获取过程信息失败");
        }
      } catch (error) {
        console.error("获取过程信息出错:", error);
        this.$message.error("获取过程信息列表失败");
        throw error;
      }
    },

    // 加载过程类型选项
    async loadProcessTypeOptions() {
      try {
        const response = await systemManagementApi.queryItemListById({
          systemDictionaryId: "202507051549",
        });

        if (response && response.code === 0) {
          this.processTypeOptions = (response.data || []).map((item) => ({
            label: item.itemName,
            value: item.id,
          }));
        } else {
          throw new Error(response?.message || "获取过程类型选项失败");
        }
      } catch (error) {
        console.error("获取过程类型选项失败:", error);
        throw error;
      }
    },

    // 过程类型变化处理
    async handleProcessTypeChange() {
      try {
        this.loading = true;
        await this.fetchProcessList();
      } catch (error) {
        console.error("过程类型过滤失败:", error);
        this.filterProcessData();
      } finally {
        this.loading = false;
      }
    },

    // 过滤过程信息数据（客户端过滤）
    filterProcessData() {
      if (!this.selectedProcessType) {
        this.filteredProcessData = [...this.processData];
      } else {
        this.filteredProcessData = this.processData.filter(
          (item) => item.processType === this.selectedProcessType
        );
      }

      // 过滤后自动应用当前排序
      this.sortProcessData();

      // 数据变化后，强制更新视图以重新计算连接线
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    // 根据过程类型ID获取名称
    getProcessTypeName(processTypeId) {
      if (!processTypeId) return "";
      const option = this.processTypeOptions.find(
        (opt) => opt.value === processTypeId
      );
      return option ? option.label : processTypeId;
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        return date.toLocaleString();
      } catch (e) {
        return dateStr;
      }
    },

    // 格式化日期时间（用于过程信息列表）
    formatDateTime(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (e) {
        return dateStr;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-content {
  min-height: 600px;
}

// 顶部核心信息区域
.header-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 20px;
  min-width: 0; // 确保flex容器能够收缩

  .core-info-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-start;

    .core-info-item {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-width: 150px;
      background: #fff;
      border: 1px solid #e1e8ed;
      border-radius: 6px;
      padding: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: all 0.2s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
        transform: translateY(-1px);
      }

      .info-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
        font-weight: 500;
      }

      .info-value {
        font-size: 14px;
        color: #333;
        font-weight: 600;
        line-height: 1.4;

        &.casualty-info {
          color: #e6a23c;
          font-weight: 700;
        }
      }
    }
  }

  // 兼容旧的样式结构
  .info-items-container {
    display: flex;
    flex-wrap: nowrap;
    gap: 12px;
    align-items: center;
    overflow-x: auto;
    padding: 4px 0;

    .info-item {
      display: flex;
      align-items: center;
      flex: 0 0 auto;
      min-width: 80px;
      white-space: nowrap;
      background: #fff;
      border: 1px solid #e1e8ed;
      border-radius: 6px;
      padding: 8px 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: all 0.2s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
        transform: translateY(-1px);
      }

      &.full-width {
        flex: 1 1 auto;
        min-width: 200px;
        margin-top: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
        border-color: #d1d9e0;
      }

      .info-label {
        font-size: 12px;
        color: #6b7280;
        margin-right: 8px;
        white-space: nowrap;
        flex-shrink: 0;
        font-weight: 500;
        background: #f3f4f6;
        padding: 2px 6px;
        border-radius: 4px;
        border: 1px solid #e5e7eb;
      }

      .info-value {
        font-size: 13px;
        color: #1f2937;
        font-weight: 600;
        line-height: 1.4;
        flex: 1;
        white-space: nowrap;

        &.status-processing {
          color: #409eff;
          background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
          border: 1px solid #90caf9;
          border-radius: 6px;
          padding: 4px 8px;
          font-weight: 600;

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }

      &.full-width {
        .info-label {
          background: #e8f4fd;
          border-color: #b3d9f2;
          color: #1565c0;
        }

        .info-value {
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: calc(100% - 80px);
        }
      }
    }
  }
}

// 主体布局区域
.main-layout {
  min-height: 500px;
}

// 左侧内容区域
.left-content {
  .core-info-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);
    }

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
      color: #1a202c;

      i {
        margin-right: 8px;
        color: #4299e1;
        font-size: 18px;
      }

      span {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .core-info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 12px;

      .core-info-item {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 16px;
        display: flex;
        align-items: flex-start;
        gap: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 3px;
          background: linear-gradient(90deg, #4299e1, #3182ce);
          transform: scaleX(0);
          transition: transform 0.3s ease;
        }

        &:hover {
          border-color: #4299e1;
          box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
          transform: translateY(-3px);

          &::before {
            transform: scaleX(1);
          }

          .info-icon {
            transform: scale(1.1);
          }
        }

        // 优先级高的项目（事件标题）
        &.priority-high {
          grid-column: 1 / -1;
          background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
          border-color: #cbd5e0;

          &::before {
            background: linear-gradient(90deg, #667eea, #764ba2);
          }

          .info-value {
            font-size: 16px;
            font-weight: 700;
            color: #2d3748;
          }
        }

        // 伤亡情况特殊样式
        &.casualty-item {
          border-color: #fed7d7;
          background: linear-gradient(135deg, #fffaf0 0%, #fef5e7 100%);

          &::before {
            background: linear-gradient(90deg, #f56565, #e53e3e);
          }

          .casualty-icon {
            color: #e53e3e;
          }
        }

        // 地点项目
        &.location-item {
          .location-icon {
            color: #38a169;
          }
        }

        .info-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
          color: #319795;
          font-size: 16px;
          flex-shrink: 0;
          transition: all 0.3s ease;

          &.type-icon {
            background: linear-gradient(135deg, #e6f3ff 0%, #bee3f8 100%);
            color: #3182ce;
          }

          &.casualty-icon {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            color: #e53e3e;
          }

          &.time-icon {
            background: linear-gradient(135deg, #faf5ff 0%, #e9d8fd 100%);
            color: #805ad5;
          }

          &.location-icon {
            background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
            color: #38a169;
          }
        }

        .info-content {
          flex: 1;
          min-width: 0;

          .info-label {
            font-size: 11px;
            color: #718096;
            margin-bottom: 2px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
          }

          .info-value {
            font-size: 13px;
            color: #2d3748;
            font-weight: 600;
            line-height: 1.3;
            word-break: break-word;

            &.title-text {
              font-size: 14px;
              font-weight: 700;
              color: #1a202c;
            }

            &.type-text {
              color: #3182ce;
              background: rgba(49, 130, 206, 0.1);
              padding: 2px 8px;
              border-radius: 4px;
              display: inline-block;
            }

            &.casualty-info {
              color: #e53e3e;
              font-weight: 700;
              font-size: 13px;
            }

            &.time-text {
              color: #805ad5;
              font-family: "Monaco", "Menlo", monospace;
            }

            &.location-text {
              color: #38a169;
            }
          }
        }
      }
    }
  }

  .process-section {
    height: 380px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
      transform: translateY(-1px);
    }
  }
}

// 右侧预留区域
.right-content {
  .reserved-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    height: 408px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(66, 153, 225, 0.03) 50%,
        transparent 70%
      );
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);

      &::before {
        transform: translateX(100%);
      }

      .reserved-content {
        i {
          color: #4299e1;
          transform: scale(1.1);
        }

        h3 {
          color: #2d3748;
        }

        p {
          color: #4a5568;
        }
      }
    }

    .reserved-content {
      text-align: center;
      color: #718096;
      z-index: 1;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #cbd5e0;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      h3 {
        font-size: 18px;
        margin: 0 0 8px 0;
        color: #4a5568;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      p {
        font-size: 14px;
        margin: 0;
        color: #718096;
        transition: all 0.3s ease;
        line-height: 1.4;
      }
    }
  }
}

// 过程信息区域样式重写
.process-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  height: 380px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, #4299e1, #3182ce, #805ad5);
    }

    .section-title-area {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .section-title {
      font-size: 16px;
      font-weight: 700;
      color: #1a202c;
      margin: 0;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      padding: 6px 8px;
      border-radius: 6px;
      position: relative;

      &.sortable {
        &:hover {
          background: linear-gradient(
            135deg,
            rgba(66, 153, 225, 0.1) 0%,
            rgba(49, 130, 206, 0.1) 100%
          );
          color: #3182ce;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
        }
      }

      i {
        margin-right: 10px;
        color: #4299e1;
        font-size: 20px;
        transition: all 0.3s ease;
      }

      &:hover i {
        color: #3182ce;
        transform: scale(1.1);
      }

      .sort-icon {
        margin-left: 10px;
        margin-right: 0;
        font-size: 16px;
        color: #718096;
        transition: all 0.3s ease;
        background: rgba(113, 128, 150, 0.1);
        padding: 4px;
        border-radius: 4px;
      }

      &:hover .sort-icon {
        color: #3182ce;
        background: rgba(49, 130, 206, 0.1);
        transform: rotate(180deg);
      }
    }

    .sort-indicator {
      font-size: 12px;
      color: #4a5568;
      margin-top: 4px;
      margin-left: 16px;
      background: linear-gradient(
        135deg,
        rgba(66, 153, 225, 0.1) 0%,
        rgba(49, 130, 206, 0.1) 100%
      );
      padding: 4px 10px;
      border-radius: 6px;
      font-weight: 600;
      border: 1px solid rgba(66, 153, 225, 0.2);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 20px;

      .filter-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-select {
          .el-input__inner {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;

            &:hover {
              border-color: #4299e1;
            }

            &:focus {
              border-color: #3182ce;
              box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
            }
          }
        }

        .el-button {
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          background: #ffffff;
          color: #4a5568;
          transition: all 0.3s ease;

          &:hover {
            border-color: #4299e1;
            color: #3182ce;
            background: rgba(66, 153, 225, 0.05);
            transform: translateY(-1px);
          }
        }
      }

      .control-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-checkbox {
          font-size: 14px;
          font-weight: 500;

          .el-checkbox__label {
            color: #4a5568;
          }

          &:hover .el-checkbox__label {
            color: #3182ce;
          }
        }
      }
    }
  }
}

// 过程信息区域样式
.process-section {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  height: 500px;
  display: flex;
  flex-direction: column;

  .process-timeline-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px;
  }
}

// 过程信息时间轴样式
.process-timeline {
  position: relative;

  // 动态主时间轴线 - 根据模块数量动态调整
  &::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 50px;
    width: 2px;
    background: linear-gradient(to bottom, #409eff 0%, #176ec5 100%);
    z-index: 1;
    // 使用CSS变量动态设置高度
    height: var(--timeline-height, 0px);
    transition: height 0.3s ease;
  }

  .process-item {
    display: flex;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding: 14px;
    align-items: flex-start;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        135deg,
        rgba(66, 153, 225, 0.02) 0%,
        rgba(49, 130, 206, 0.02) 100%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: #4299e1;
      box-shadow: 0 8px 20px rgba(66, 153, 225, 0.15);
      transform: translateY(-2px);

      &::before {
        opacity: 1;
      }

      .process-number-wrapper {
        .process-number {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(66, 153, 225, 0.35);

          &::before {
            opacity: 1;
            transform: scale(1);
          }

          &::after {
            opacity: 1;
            transform: scale(1);
          }
        }

        .process-line {
          opacity: 1;
          background: linear-gradient(
            to bottom,
            #4299e1 0%,
            #3182ce 50%,
            #805ad5 100%
          );
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    // 序号包装器
    .process-number-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 16px;
      position: relative;
      flex-shrink: 0;
      z-index: 2;

      .process-number {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        color: #ffffff;
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
        border: 2px solid #ffffff;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);

        // 数字圆圈的光晕效果
        &::before {
          content: "";
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border-radius: 50%;
          background: linear-gradient(
            135deg,
            rgba(66, 153, 225, 0.2) 0%,
            rgba(49, 130, 206, 0.2) 100%
          );
          z-index: -1;
          opacity: 0;
          transition: all 0.4s ease;
          transform: scale(0.8);
        }

        &::after {
          content: "";
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          border-radius: 50%;
          border: 2px solid rgba(66, 153, 225, 0.3);
          opacity: 0;
          transition: all 0.4s ease;
          transform: scale(1.2);
        }
      }

      .process-line {
        width: 2px;
        background: linear-gradient(to bottom, #4299e1 0%, #3182ce 100%);
        margin-top: 6px;
        flex: 1;
        min-height: 40px;
        opacity: 0.6;
        transition: all 0.3s ease;
      }
    }

    // 内容包装器
    .process-content-wrapper {
      display: flex;
      flex: 1;
      align-items: flex-start;
      gap: 12px;
    }

    .process-left {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20px;
      position: relative;
      z-index: 3;

      .process-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #409eff 0%, #1976d2 100%);
        color: #fff;
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        position: relative;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        border: 3px solid #fff;
        transition: all 0.3s ease;

        // 数字圆圈的光晕效果
        &::before {
          content: "";
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border-radius: 50%;
          background: linear-gradient(
            135deg,
            rgba(64, 158, 255, 0.2) 0%,
            rgba(25, 118, 210, 0.2) 100%
          );
          z-index: -1;
          opacity: 0;
          transition: all 0.3s ease;
        }
      }

      // 移除原来的连接线，因为现在使用主时间轴线
      .process-line {
        display: none;
      }
    }

    // 悬停和激活状态的数字样式
    &:hover .process-left .process-number {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);

      &::before {
        opacity: 1;
        transform: scale(1.2);
      }
    }

    &.active .process-left .process-number {
      background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
      transform: scale(1.15);
      box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);

      &::before {
        opacity: 1;
        transform: scale(1.3);
        background: linear-gradient(
          135deg,
          rgba(255, 107, 53, 0.3) 0%,
          rgba(247, 147, 30, 0.3) 100%
        );
      }
    }

    .process-right {
      flex: 1;
      position: relative;
      padding-left: 4px;

      .process-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
        flex-wrap: wrap;
        gap: 8px;

        .process-time {
          font-size: 15px;
          color: #1a1a1a;
          font-weight: 700;
          background: linear-gradient(135deg, #409eff 0%, #1976d2 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          flex-shrink: 0;
        }

        .process-unit {
          font-size: 13px;
          color: #409eff;
          font-weight: 600;
          background: rgba(64, 158, 255, 0.1);
          padding: 4px 10px;
          border-radius: 16px;
          border: 1px solid rgba(64, 158, 255, 0.2);
          flex-shrink: 0;
        }

        .process-source {
          font-size: 11px;
          color: #666;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          padding: 4px 10px;
          border-radius: 14px;
          border: 1px solid #e9ecef;
          font-weight: 500;
          flex-shrink: 0;
        }
      }

      .process-content {
        .process-description {
          font-size: 14px;
          color: #4a5568;
          line-height: 1.6;
          margin-bottom: 0;
          padding: 8px 0;
          border-left: 3px solid transparent;
          padding-left: 12px;
          transition: all 0.3s ease;
          background: rgba(248, 250, 252, 0.5);
          border-radius: 0 8px 8px 0;
          margin-left: -12px;
          padding-left: 16px;
        }
      }

      .process-expand-icon {
        position: absolute;
        right: 16px;
        top: 75%;
        transform: translateY(-50%);
        color: #cbd5e0;
        font-size: 18px;
        transition: all 0.3s ease;
        cursor: pointer;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid #e2e8f0;

        i {
          transition: transform 0.3s ease;
        }

        &:hover {
          color: #409eff;
          background: rgba(64, 158, 255, 0.1);
          border-color: #409eff;
          transform: translateY(-50%) scale(1.1);
        }
      }
    }

    // 悬停状态的内容样式
    &:hover .process-right .process-content .process-description {
      border-left-color: #409eff;
      background: rgba(64, 158, 255, 0.05);
    }

    // 激活状态的内容样式
    &.active .process-right .process-content .process-description {
      border-left-color: #ff6b35;
      background: rgba(255, 107, 53, 0.05);
      color: #2d3748;
      font-weight: 500;
    }

    // 新布局样式
    // 左侧信息区：时间和操作人
    .process-left-info {
      display: flex;
      flex-direction: column;
      min-width: 100px;
      flex-shrink: 0;
      padding: 2px 0;

      .process-time {
        font-size: 13px;
        color: #666;
        margin-bottom: 4px;
        font-weight: 500;
      }

      .process-operator {
        font-size: 12px;
        color: #999;
        font-weight: 400;
      }
    }

    // 中间内容区：详细信息
    .process-main-content {
      flex: 1;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      background: rgba(248, 250, 252, 0.3);
      border: 1px solid transparent;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: linear-gradient(to bottom, #4299e1, #3182ce);
        border-radius: 2px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        background: rgba(66, 153, 225, 0.05);
        border-color: rgba(66, 153, 225, 0.2);
        transform: translateX(3px);

        &::before {
          opacity: 1;
        }
      }

      .process-description {
        font-size: 14px;
        color: #2d3748;
        line-height: 1.5;
        margin: 0;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      &:hover .process-description {
        color: #1a202c;
      }
    }

    // 右侧功能区：操作类型和复选框
    .process-right-actions {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;
      min-width: 90px;
      flex-shrink: 0;
      padding: 2px 0;

      .operation-type {
        font-size: 11px;
        color: #ffffff;
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        padding: 4px 8px;
        border-radius: 6px;
        font-weight: 600;
        white-space: nowrap;
        box-shadow: 0 2px 6px rgba(66, 153, 225, 0.25);
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.3px;

        &:hover {
          background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
          transform: translateY(-1px);
          box-shadow: 0 3px 8px rgba(66, 153, 225, 0.35);
        }
      }

      .el-checkbox {
        transform: scale(1.1);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }

  // 步骤轴进入动画
  .process-item {
    animation: slideInFromLeft 0.6s ease-out both;

    &:nth-child(1) {
      animation-delay: 0.1s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.3s;
    }
    &:nth-child(4) {
      animation-delay: 0.4s;
    }
    &:nth-child(5) {
      animation-delay: 0.5s;
    }
    &:nth-child(n + 6) {
      animation-delay: 0.6s;
    }
  }
}

// 步骤轴动画关键帧
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// 数字脉动动画
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 激活状态的数字添加脉动效果
.process-timeline .process-item.active .process-left .process-number {
  animation: pulse 2s infinite;
}

// 过程详情弹框样式
.process-detail-dialog {
  z-index: 3000 !important;

  .el-dialog {
    margin-top: 5vh !important;
  }

  .el-dialog__wrapper {
    z-index: 3000 !important;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.process-detail-content {
  min-height: 400px;
  max-height: 70vh;
  overflow-y: auto;

  .detail-content-area {
    padding: 20px;

    .detail-item {
      margin-bottom: 16px;
      padding: 8px 0;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(64, 158, 255, 0.02);
        padding-left: 8px;
        margin-left: -8px;
        margin-right: -8px;
      }

      &.full-content {
        margin-bottom: 20px;
      }

      .detail-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 6px;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .detail-value {
        font-size: 14px;
        color: #333;
        line-height: 1.5;
        transition: all 0.3s ease;

        &.detail-textarea {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 12px;
          min-height: 80px;
          line-height: 1.6;
          transition: all 0.3s ease;

          &:hover {
            background: #f0f7ff;
            border-color: #409eff;
          }
        }
      }
    }

    .casualty-summary {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-top: 16px;
      padding: 12px;
      background: linear-gradient(135deg, #f8f9fa 0%, #f0f7ff 100%);
      border-radius: 6px;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
      }

      .casualty-item {
        display: flex;
        align-items: center;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }

        .casualty-label {
          font-size: 13px;
          color: #666;
          margin-right: 4px;
        }

        .casualty-number {
          font-size: 16px;
          font-weight: 600;
          padding: 4px 10px;
          border-radius: 6px;
          transition: all 0.3s ease;
          cursor: default;

          &:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          &.death {
            color: #f56c6c;
            background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
            border: 1px solid #fecaca;
          }

          &.missing {
            color: #e6a23c;
            background: linear-gradient(135deg, #fdf6ec 0%, #fed7aa 100%);
            border: 1px solid #fde68a;
          }

          &.severe {
            color: #f56c6c;
            background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
            border: 1px solid #fecaca;
          }

          &.light {
            color: #67c23a;
            background: linear-gradient(135deg, #f0f9eb 0%, #dcfce7 100%);
            border: 1px solid #bbf7d0;
          }
        }
      }
    }
  }

  .no-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #999;
    transition: all 0.3s ease;

    i {
      font-size: 48px;
      margin-bottom: 12px;
      color: #ddd;
      transition: all 0.3s ease;
      animation: pulse 2s infinite;
    }

    p {
      font-size: 14px;
      margin: 0;
      transition: color 0.3s ease;
    }

    &:hover {
      color: #666;

      i {
        color: #409eff;
        transform: scale(1.1);
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 15px;
  }

  p {
    font-size: 14px;
  }
}

// 详情项动画
.detail-content-area .detail-item,
.detail-content-area .casualty-summary {
  animation: fadeInUp 0.3s ease-out both;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式处理
@media (max-width: 1400px) {
  .header-info .info-items-container {
    gap: 8px;

    .info-item {
      min-width: 60px;
      padding: 6px 10px;

      .info-label {
        font-size: 11px;
        padding: 1px 4px;
        margin-right: 6px;
      }

      .info-value {
        font-size: 12px;
      }

      &.full-width .info-value {
        max-width: calc(100% - 70px);
      }
    }
  }
}

@media (max-width: 1200px) {
  .header-info .info-items-container {
    flex-wrap: wrap;

    .info-item.full-width {
      flex: 1 1 100%;
      margin-top: 6px;
      white-space: normal;

      .info-value {
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
        max-width: none;
      }
    }
  }
}
</style>
